import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:thingsboard_app/utils/app_config.dart';

class AwsApiHelper {
  static final Dio dio = Dio();

  static Future<bool> registerUser(
    String phoneNo,
    String gatewayId,
    String fcmId,
    String refererNo,
  ) async {
    try {
      Response response = await dio.post(
        AppConfig.api.registerUser,
        data: {
          "phone_no": phoneNo,
          "gateway_id": gatewayId,
          "fcm_id": fcmId,
          "referer_no": refererNo,
        },
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      log('AwsApiHelper::registerUser error: $e');
      return false;
    }
  }

  static Future<bool> updateFcm(
    String phNumber,
    String refererNumber,
    String fcmId,
  ) async {
    try {
      Response response = await dio.post(
        AppConfig.api.updateFcm,
        data: {
          "phone_no": phNumber,
          "referer_no": referer<PERSON><PERSON>ber,
          "fcm_id": fcmId,
        },
      );

      if (response.statusCode == 200) {
        log('AwsApiHelper::updateFcm success for phone: $phNumber');
        return true;
      } else {
        log(
          'AwsApiHelper::updateFcm failed with status: ${response.statusCode}',
        );
        return false;
      }
    } catch (e) {
      log('AwsApiHelper::updateFcm error: $e');
      return false;
    }
  }

  static Future<bool> registerSecondaryUser(
    String phoneNo,
    String refererNo,
    String fcmId,
  ) async {
    try {
      Response response = await dio.post(
        AppConfig.api.registerSecondaryUser,
        data: {
          "phone_no": phoneNo,
          "referer_no": refererNo,
          if (fcmId.isNotEmpty) "fcm_id": fcmId,
        },
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      log('AwsApiHelper::registerSecondaryUser error: $e');
      return false;
    }
  }

  static Future<bool> existingUserLogin(String phoneNo) async {
    try {
      Response response = await dio.post(
        AppConfig.api.existingUserLogin,
        data: {"phone_no": phoneNo},
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      log('AwsApiHelper::existingUserLogin error: $e');
      return false;
    }
  }

  static Future<bool> getSecondaryUsers(String gatewayId) async {
    try {
      Response response = await dio.post(
        AppConfig.api.getSecondaryUsers,
        data: {"gateway_id": gatewayId},
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      log('AwsApiHelper::getSecondaryUsers error: $e');
      return false;
    }
  }

  static Future<bool> updateUser(
    String oldNumber,
    String newNumber,
    String gatewayId,
  ) async {
    try {
      Response response = await dio.post(
        AppConfig.api.updateUser,
        data: {
          "old_phone_no": oldNumber,
          "new_phone_no": newNumber,
          "gateway_id": gatewayId,
        },
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      log('AwsApiHelper::updateUser error: $e');
      return false;
    }
  }

  static Future<bool> factoryReset(String gatewayId) async {
    try {
      Response response = await dio.post(
        AppConfig.api.factoryReset,
        data: {"gateway_id": gatewayId},
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      log('AwsApiHelper::factoryReset error: $e');
      return false;
    }
  }

  static Future<bool> saveUser(dynamic saveUserDetails, String phNumber) async {
    try {
      Response response = await dio.post(
        AppConfig.api.saveUser,
        data: {
          "secondary_usr": saveUserDetails.getSuser().toString(),
          "gateway_id": saveUserDetails.getGatewayid(),
          "phone_number": phNumber,
        },
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      log('AwsApiHelper::saveUser error: $e');
      return false;
    }
  }

  static Future<bool> getUserDataSheet(String phoneNo) async {
    try {
      Response response = await dio.post(
        AppConfig.api.getUserDataSheet,
        data: {"phone_no": phoneNo},
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      log('AwsApiHelper::getUserDataSheet error: $e');
      return false;
    }
  }

  static Future<bool> clearUserDetails(String phoneNo) async {
    try {
      Response response = await dio.post(
        AppConfig.api.clearUserDetails,
        data: {"phone_no": phoneNo},
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      log('AwsApiHelper::clearUserDetails error: $e');
      return false;
    }
  }

  static Future<bool> changeSim(String oldNumber, String newNumber) async {
    try {
      Response response = await dio.post(
        AppConfig.api.changeSim,
        data: {"old_phone_no": oldNumber, "new_phone_no": newNumber},
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      log('AwsApiHelper::changeSim error: $e');
      return false;
    }
  }

  static Future<bool> deleteSecondaryUser(
    String secondaryPhoneNo,
    String gatewayId,
  ) async {
    try {
      Response response = await dio.post(
        AppConfig.api.deleteSecondaryUser,
        data: {"secondary_phone_no": secondaryPhoneNo, "gateway_id": gatewayId},
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      log('AwsApiHelper::deleteSecondaryUser error: $e');
      return false;
    }
  }

  static Future<bool> addDeviceRuleChain(
    String deviceName,
    String deviceAccessNumber,
    String customerName,
  ) async {
    try {
      Response response = await dio.post(
        AppConfig.api.addDeviceRuleChain,
        data: {
          "deviceName": deviceName,
          "access": deviceAccessNumber,
          "customerName": customerName,
        },
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      log('AwsApiHelper::addDeviceRuleChain error: $e');
      return false;
    }
  }

  static Future<bool> deleteDeviceRuleChain(
    String deviceName,
    String deviceAccessNumber,
    String customerName,
  ) async {
    try {
      Response response = await dio.post(
        AppConfig.api.deleteDeviceRuleChain,
        data: {
          "deviceName": deviceName,
          "access": deviceAccessNumber,
          "customerName": customerName,
        },
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      log('AwsApiHelper::deleteDeviceRuleChain error: $e');
      return false;
    }
  }

  static Map<String, dynamic> getCommonParams() {
    Map<String, dynamic> params = {};
    return params;
  }
}
