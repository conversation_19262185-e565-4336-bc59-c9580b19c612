import 'package:shared_preferences/shared_preferences.dart';
import 'package:thingsboard_app/core/logger/tb_logger.dart';
import 'package:thingsboard_app/locator.dart';

/// Service to manage FCM configuration settings like phone numbers and gateway IDs
/// This allows users to configure their phone number and gateway ID for AWS API calls
class FcmConfigService {
  static final FcmConfigService _instance = FcmConfigService._();
  late TbLogger _log;

  FcmConfigService._();

  factory FcmConfigService() => _instance;

  static const String _phoneNumberKey = 'fcm_phone_number';
  static const String _gatewayIdKey = 'fcm_gateway_id';
  static const String _refererNumberKey = 'fcm_referer_number';

  void init() {
    _log = getIt<TbLogger>();
  }

  /// Set the phone number for FCM registration
  Future<void> setPhoneNumber(String phoneNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_phoneNumberKey, phoneNumber);
      _log.debug('FcmConfigService::setPhoneNumber() set to: $phoneNumber');
    } catch (e) {
      _log.error('FcmConfigService::setPhoneNumber() error: $e');
    }
  }

  /// Get the configured phone number
  Future<String?> getPhoneNumber() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final phoneNumber = prefs.getString(_phoneNumberKey);
      _log.debug('FcmConfigService::getPhoneNumber() returning: $phoneNumber');
      return phoneNumber;
    } catch (e) {
      _log.error('FcmConfigService::getPhoneNumber() error: $e');
      return null;
    }
  }

  /// Set the gateway ID for FCM registration
  Future<void> setGatewayId(String gatewayId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_gatewayIdKey, gatewayId);
      _log.debug('FcmConfigService::setGatewayId() set to: $gatewayId');
    } catch (e) {
      _log.error('FcmConfigService::setGatewayId() error: $e');
    }
  }

  /// Get the configured gateway ID
  Future<String?> getGatewayId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final gatewayId = prefs.getString(_gatewayIdKey);
      _log.debug('FcmConfigService::getGatewayId() returning: $gatewayId');
      return gatewayId;
    } catch (e) {
      _log.error('FcmConfigService::getGatewayId() error: $e');
      return null;
    }
  }

  /// Set the referer number for FCM registration
  Future<void> setRefererNumber(String refererNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_refererNumberKey, refererNumber);
      _log.debug('FcmConfigService::setRefererNumber() set to: $refererNumber');
    } catch (e) {
      _log.error('FcmConfigService::setRefererNumber() error: $e');
    }
  }

  /// Get the configured referer number
  Future<String?> getRefererNumber() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final refererNumber = prefs.getString(_refererNumberKey);
      _log.debug('FcmConfigService::getRefererNumber() returning: $refererNumber');
      return refererNumber;
    } catch (e) {
      _log.error('FcmConfigService::getRefererNumber() error: $e');
      return null;
    }
  }

  /// Check if all required configuration is available
  Future<bool> isConfigured() async {
    final phoneNumber = await getPhoneNumber();
    final gatewayId = await getGatewayId();
    final refererNumber = await getRefererNumber();

    final isConfigured = phoneNumber != null && 
                        phoneNumber.isNotEmpty &&
                        gatewayId != null && 
                        gatewayId.isNotEmpty &&
                        refererNumber != null && 
                        refererNumber.isNotEmpty;

    _log.debug('FcmConfigService::isConfigured() returning: $isConfigured');
    return isConfigured;
  }

  /// Clear all configuration
  Future<void> clearConfiguration() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_phoneNumberKey);
      await prefs.remove(_gatewayIdKey);
      await prefs.remove(_refererNumberKey);
      _log.debug('FcmConfigService::clearConfiguration() cleared all config');
    } catch (e) {
      _log.error('FcmConfigService::clearConfiguration() error: $e');
    }
  }

  /// Set all configuration at once
  Future<void> setConfiguration({
    required String phoneNumber,
    required String gatewayId,
    required String refererNumber,
  }) async {
    await setPhoneNumber(phoneNumber);
    await setGatewayId(gatewayId);
    await setRefererNumber(refererNumber);
    _log.debug('FcmConfigService::setConfiguration() set all config');
  }

  /// Get all configuration as a map
  Future<Map<String, String?>> getConfiguration() async {
    return {
      'phoneNumber': await getPhoneNumber(),
      'gatewayId': await getGatewayId(),
      'refererNumber': await getRefererNumber(),
    };
  }

  /// Extract phone number from email if possible
  String? extractPhoneFromEmail(String? email) {
    if (email == null || email.isEmpty) return null;
    
    // Try to extract phone number from email
    // This is a simple regex to find 10-digit numbers in email
    final phoneRegex = RegExp(r'\b\d{10}\b');
    final match = phoneRegex.firstMatch(email);
    final extractedPhone = match?.group(0);
    
    _log.debug('FcmConfigService::extractPhoneFromEmail() email: $email, extracted: $extractedPhone');
    return extractedPhone;
  }

  /// Auto-configure from user email if possible
  Future<bool> autoConfigureFromEmail(String? email) async {
    final extractedPhone = extractPhoneFromEmail(email);
    if (extractedPhone != null) {
      await setPhoneNumber(extractedPhone);
      await setRefererNumber(extractedPhone); // Use same number as referer
      _log.debug('FcmConfigService::autoConfigureFromEmail() auto-configured phone: $extractedPhone');
      return true;
    }
    return false;
  }
}
