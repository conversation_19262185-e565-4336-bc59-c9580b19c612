import 'package:thingsboard_app/core/logger/tb_logger.dart';
import 'package:thingsboard_app/locator.dart';
import 'package:thingsboard_app/utils/services/aws_api_helper.dart';
import 'package:thingsboard_app/utils/services/notification_service.dart';

class UserRegistrationService {
  static final UserRegistrationService _instance = UserRegistrationService._();
  late TbLogger _log;

  UserRegistrationService._();

  factory UserRegistrationService() => _instance;

  void init() {
    _log = getIt<TbLogger>();
  }

  /// Register a new user with AWS server
  /// This should be called when a new user is created or when FCM setup is needed
  Future<bool> registerUserWithAws({
    required String phoneNumber,
    required String gatewayId,
    required String refererNumber,
  }) async {
    try {
      _log.debug(
        'UserRegistrationService::registerUserWithAws() phone: $phoneNumber, gateway: $gatewayId',
      );

      // Get current FCM token
      final fcmToken = await NotificationService().getToken();
      if (fcmToken == null) {
        _log.warn(
          'UserRegistrationService::registerUserWithAws() no FCM token available',
        );
        return false;
      }

      // Register user with AWS
      final success = await AwsApiHelper.registerUser(
        phoneNumber,
        gatewayId,
        fcmToken,
        refererNumber,
      );

      if (success) {
        _log.debug('UserRegistrationService::registerUserWithAws() success');
      } else {
        _log.warn('UserRegistrationService::registerUserWithAws() failed');
      }

      return success;
    } catch (e) {
      _log.error('UserRegistrationService::registerUserWithAws() error: $e');
      return false;
    }
  }

  /// Register a secondary user with AWS server
  Future<bool> registerSecondaryUserWithAws({
    required String phoneNumber,
    required String refererNumber,
  }) async {
    try {
      _log.debug(
        'UserRegistrationService::registerSecondaryUserWithAws() phone: $phoneNumber, referer: $refererNumber',
      );

      // Get current FCM token
      final fcmToken = await NotificationService().getToken();
      if (fcmToken == null) {
        _log.warn(
          'UserRegistrationService::registerSecondaryUserWithAws() no FCM token available',
        );
        return false;
      }

      // Register secondary user with AWS
      final success = await AwsApiHelper.registerSecondaryUser(
        phoneNumber,
        refererNumber,
        fcmToken,
      );

      if (success) {
        _log.debug(
          'UserRegistrationService::registerSecondaryUserWithAws() success',
        );
      } else {
        _log.warn(
          'UserRegistrationService::registerSecondaryUserWithAws() failed',
        );
      }

      return success;
    } catch (e) {
      _log.error(
        'UserRegistrationService::registerSecondaryUserWithAws() error: $e',
      );
      return false;
    }
  }

  /// Update FCM token for existing user
  Future<bool> updateFcmTokenForUser({
    required String phoneNumber,
    required String refererNumber,
  }) async {
    try {
      _log.debug(
        'UserRegistrationService::updateFcmTokenForUser() phone: $phoneNumber',
      );

      // Get current FCM token
      final fcmToken = await NotificationService().getToken();
      if (fcmToken == null) {
        _log.warn(
          'UserRegistrationService::updateFcmTokenForUser() no FCM token available',
        );
        return false;
      }

      // Update FCM token with AWS
      final success = await AwsApiHelper.updateFcm(
        phoneNumber,
        refererNumber,
        fcmToken,
      );

      if (success) {
        _log.debug('UserRegistrationService::updateFcmTokenForUser() success');
      } else {
        _log.warn('UserRegistrationService::updateFcmTokenForUser() failed');
      }

      return success;
    } catch (e) {
      _log.error('UserRegistrationService::updateFcmTokenForUser() error: $e');
      return false;
    }
  }

  /// Check if user exists in AWS system
  Future<bool> checkExistingUser(String phoneNumber) async {
    try {
      _log.debug(
        'UserRegistrationService::checkExistingUser() phone: $phoneNumber',
      );

      final success = await AwsApiHelper.existingUserLogin(phoneNumber);

      if (success) {
        _log.debug('UserRegistrationService::checkExistingUser() user exists');
      } else {
        _log.debug(
          'UserRegistrationService::checkExistingUser() user does not exist',
        );
      }

      return success;
    } catch (e) {
      _log.error('UserRegistrationService::checkExistingUser() error: $e');
      return false;
    }
  }

  /// Get user data sheet from AWS
  Future<bool> getUserDataSheet(String phoneNumber) async {
    try {
      _log.debug(
        'UserRegistrationService::getUserDataSheet() phone: $phoneNumber',
      );

      final success = await AwsApiHelper.getUserDataSheet(phoneNumber);

      if (success) {
        _log.debug('UserRegistrationService::getUserDataSheet() success');
      } else {
        _log.warn('UserRegistrationService::getUserDataSheet() failed');
      }

      return success;
    } catch (e) {
      _log.error('UserRegistrationService::getUserDataSheet() error: $e');
      return false;
    }
  }

  /// Clear user details from AWS
  Future<bool> clearUserDetails(String phoneNumber) async {
    try {
      _log.debug(
        'UserRegistrationService::clearUserDetails() phone: $phoneNumber',
      );

      final success = await AwsApiHelper.clearUserDetails(phoneNumber);

      if (success) {
        _log.debug('UserRegistrationService::clearUserDetails() success');
      } else {
        _log.warn('UserRegistrationService::clearUserDetails() failed');
      }

      return success;
    } catch (e) {
      _log.error('UserRegistrationService::clearUserDetails() error: $e');
      return false;
    }
  }

  /// Factory reset for gateway
  Future<bool> factoryReset(String gatewayId) async {
    try {
      _log.debug('UserRegistrationService::factoryReset() gateway: $gatewayId');

      final success = await AwsApiHelper.factoryReset(gatewayId);

      if (success) {
        _log.debug('UserRegistrationService::factoryReset() success');
      } else {
        _log.warn('UserRegistrationService::factoryReset() failed');
      }

      return success;
    } catch (e) {
      _log.error('UserRegistrationService::factoryReset() error: $e');
      return false;
    }
  }

  /// Update user phone number
  Future<bool> updateUserPhoneNumber({
    required String oldNumber,
    required String newNumber,
    required String gatewayId,
  }) async {
    try {
      _log.debug(
        'UserRegistrationService::updateUserPhoneNumber() old: $oldNumber, new: $newNumber',
      );

      final success = await AwsApiHelper.updateUser(
        oldNumber,
        newNumber,
        gatewayId,
      );

      if (success) {
        _log.debug('UserRegistrationService::updateUserPhoneNumber() success');
      } else {
        _log.warn('UserRegistrationService::updateUserPhoneNumber() failed');
      }

      return success;
    } catch (e) {
      _log.error('UserRegistrationService::updateUserPhoneNumber() error: $e');
      return false;
    }
  }

  /// Change SIM card
  Future<bool> changeSim({
    required String oldNumber,
    required String newNumber,
  }) async {
    try {
      _log.debug(
        'UserRegistrationService::changeSim() old: $oldNumber, new: $newNumber',
      );

      final success = await AwsApiHelper.changeSim(oldNumber, newNumber);

      if (success) {
        _log.debug('UserRegistrationService::changeSim() success');
      } else {
        _log.warn('UserRegistrationService::changeSim() failed');
      }

      return success;
    } catch (e) {
      _log.error('UserRegistrationService::changeSim() error: $e');
      return false;
    }
  }

  /// Delete secondary user
  Future<bool> deleteSecondaryUser({
    required String secondaryPhoneNumber,
    required String gatewayId,
  }) async {
    try {
      _log.debug(
        'UserRegistrationService::deleteSecondaryUser() phone: $secondaryPhoneNumber, gateway: $gatewayId',
      );

      final success = await AwsApiHelper.deleteSecondaryUser(
        secondaryPhoneNumber,
        gatewayId,
      );

      if (success) {
        _log.debug('UserRegistrationService::deleteSecondaryUser() success');
      } else {
        _log.warn('UserRegistrationService::deleteSecondaryUser() failed');
      }

      return success;
    } catch (e) {
      _log.error('UserRegistrationService::deleteSecondaryUser() error: $e');
      return false;
    }
  }
}
