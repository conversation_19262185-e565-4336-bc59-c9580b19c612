enum Environment { dev, prod }

class AppConfig {
  static Environment currentEnvironment = Environment.prod;
  static String thingsBoardUrl = 'https://app.sunrider.ai';
  static APIs get api => APIs.fromEnvironment(currentEnvironment);
}

class APIs {
  static String baseUrl = '';

  String login;
  String registerUser;
  String getOTP;
  String getSecondaryUsers;
  String updateUser;
  String factoryReset;
  String saveUser;
  String getUserDataSheet;
  String clearUserDetails;
  String registerSecondaryUser;
  String existingUserLogin;
  String userFactoryReset;
  String changeSim;
  String updateFcm;
  String deleteSecondaryUser;
  String addDeviceRuleChain;
  String deleteDeviceRuleChain;

  APIs({
    required this.login,
    required this.registerUser,
    required this.getOTP,
    required this.getSecondaryUsers,
    required this.updateUser,
    required this.factoryReset,
    required this.saveUser,
    required this.getUserDataSheet,
    required this.clearUserDetails,
    required this.registerSecondaryUser,
    required this.existingUserLogin,
    required this.userFactoryReset,
    required this.changeSim,
    required this.updateFcm,
    required this.deleteSecondaryUser,
    required this.addDeviceRuleChain,
    required this.deleteDeviceRuleChain,
  });

  factory APIs.fromEnvironment(Environment environment) {
    switch (environment) {
      case Environment.dev:
        baseUrl = 'https://sem0n6arji.execute-api.ap-south-1.amazonaws.com';
        return APIs(
          login: '$baseUrl/beta/login',
          registerUser: '$baseUrl/beta/usr/create/',
          getOTP: '$baseUrl/beta/otp/',
          getSecondaryUsers: '$baseUrl/beta/get/secondary/phoneno/',
          updateUser: '$baseUrl/beta/update/secondary/phoneno/',
          factoryReset: '$baseUrl/beta/factoryreset/',
          saveUser: '$baseUrl/beta/add/usr/',
          getUserDataSheet: '$baseUrl/beta/usr/list/',
          clearUserDetails: '$baseUrl/beta/delete/usr/',
          registerSecondaryUser: '$baseUrl/beta/add/secondary/usr/',
          existingUserLogin: '$baseUrl/beta/existing/usr/',
          userFactoryReset: '$baseUrl/beta/factoryreset/',
          changeSim: '$baseUrl/beta/update/phoneno/',
          updateFcm: '$baseUrl/beta/update/fcmid/',
          deleteSecondaryUser: '$baseUrl/beta/delete/secondary/phoneno/',
          addDeviceRuleChain: '$baseUrl/beta/change/customer/',
          deleteDeviceRuleChain: '$baseUrl/beta/change/tenant/',
        );
      case Environment.prod:
        baseUrl = 'https://sem0n6arji.execute-api.ap-south-1.amazonaws.com';
        return APIs(
          login: '$baseUrl/api/login',
          registerUser: '$baseUrl/api/usr/create/',
          getOTP: '$baseUrl/api/otp/',
          getSecondaryUsers: '$baseUrl/api/get/secondary/phoneno/',
          updateUser: '$baseUrl/api/update/secondary/phoneno/',
          factoryReset: '$baseUrl/api/factoryreset/',
          saveUser: '$baseUrl/api/add/usr/',
          getUserDataSheet: '$baseUrl/api/usr/list/',
          clearUserDetails: '$baseUrl/api/delete/usr/',
          registerSecondaryUser: '$baseUrl/api/add/secondary/usr/',
          existingUserLogin: '$baseUrl/api/existing/usr/',
          userFactoryReset: '$baseUrl/api/factoryreset/',
          changeSim: '$baseUrl/api/update/phoneno/',
          updateFcm: '$baseUrl/api/update/fcmid/',
          deleteSecondaryUser: '$baseUrl/api/delete/secondary/phoneno/',
          addDeviceRuleChain: '$baseUrl/api/change/customer/',
          deleteDeviceRuleChain: '$baseUrl/api/change/tenant/',
        );
    }
  }
}
