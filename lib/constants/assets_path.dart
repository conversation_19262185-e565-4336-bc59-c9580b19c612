abstract class ThingsboardImage {
  static final thingsBoardWithTitle = 'assets/images/sunrider_with_title.png';
  static final thingsboard = 'assets/images/sunrider.png';
  static final thingsboardOuter = 'assets/images/thingsboard_outer.svg';
  static final thingsboardCenter = 'assets/images/sunrider.png';
  static final dashboardPlaceholder = 'assets/images/dashboard-placeholder.svg';
  static final deviceProfilePlaceholder =
      'assets/images/device-profile-placeholder.svg';

  static final oauth2Logos = <String, String>{
    'google-logo': 'assets/images/google-logo.svg',
    'github-logo': 'assets/images/github-logo.svg',
    'facebook-logo': 'assets/images/facebook-logo.svg',
    'apple-logo': 'assets/images/apple-logo.svg',
    'qr-code-logo': 'assets/images/qr_code_scanner.svg',
    'qr-code': 'assets/images/qr_code_scanner2.svg',
  };
}
