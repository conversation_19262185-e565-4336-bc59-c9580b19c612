import 'package:flutter/material.dart';
import 'package:thingsboard_app/locator.dart';
import 'package:thingsboard_app/utils/services/fcm_config_service.dart';
import 'package:thingsboard_app/utils/services/user_registration_service.dart';

/// Dialog widget for configuring FCM settings
class FcmConfigDialog extends StatefulWidget {
  const FcmConfigDialog({super.key});

  @override
  State<FcmConfigDialog> createState() => _FcmConfigDialogState();
}

class _FcmConfigDialogState extends State<FcmConfigDialog> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _gatewayController = TextEditingController();
  final _refererController = TextEditingController();
  
  bool _isLoading = false;
  bool _isRegistering = false;

  @override
  void initState() {
    super.initState();
    _loadCurrentConfig();
  }

  Future<void> _loadCurrentConfig() async {
    final configService = getIt<FcmConfigService>();
    final config = await configService.getConfiguration();
    
    setState(() {
      _phoneController.text = config['phoneNumber'] ?? '';
      _gatewayController.text = config['gatewayId'] ?? '';
      _refererController.text = config['refererNumber'] ?? '';
    });
  }

  Future<void> _saveConfig() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final configService = getIt<FcmConfigService>();
      await configService.setConfiguration(
        phoneNumber: _phoneController.text.trim(),
        gatewayId: _gatewayController.text.trim(),
        refererNumber: _refererController.text.trim(),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('FCM configuration saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving configuration: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _registerUser() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isRegistering = true;
    });

    try {
      // First save the configuration
      final configService = getIt<FcmConfigService>();
      await configService.setConfiguration(
        phoneNumber: _phoneController.text.trim(),
        gatewayId: _gatewayController.text.trim(),
        refererNumber: _refererController.text.trim(),
      );

      // Then register the user
      final registrationService = getIt<UserRegistrationService>();
      final success = await registrationService.registerUserWithAws(
        phoneNumber: _phoneController.text.trim(),
        gatewayId: _gatewayController.text.trim(),
        refererNumber: _refererController.text.trim(),
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('User registered successfully with AWS'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop(true);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to register user with AWS'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error registering user: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRegistering = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _gatewayController.dispose();
    _refererController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('FCM Configuration'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Configure your phone number and gateway ID for FCM notifications.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone Number',
                hintText: '1234567890',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Phone number is required';
                }
                if (!RegExp(r'^\d{10}$').hasMatch(value.trim())) {
                  return 'Please enter a valid 10-digit phone number';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _gatewayController,
              decoration: const InputDecoration(
                labelText: 'Gateway ID',
                hintText: 'gateway123',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Gateway ID is required';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _refererController,
              decoration: const InputDecoration(
                labelText: 'Referer Number',
                hintText: '1234567890',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Referer number is required';
                }
                if (!RegExp(r'^\d{10}$').hasMatch(value.trim())) {
                  return 'Please enter a valid 10-digit referer number';
                }
                return null;
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading || _isRegistering ? null : () {
            Navigator.of(context).pop(false);
          },
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: _isLoading || _isRegistering ? null : _saveConfig,
          child: _isLoading 
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Text('Save'),
        ),
        ElevatedButton(
          onPressed: _isLoading || _isRegistering ? null : _registerUser,
          child: _isRegistering 
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Text('Save & Register'),
        ),
      ],
    );
  }

  /// Static method to show the dialog
  static Future<bool?> show(BuildContext context) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => const FcmConfigDialog(),
    );
  }
}
