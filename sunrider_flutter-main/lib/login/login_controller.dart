import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:solar_rooftop/api_helper/api_helper.dart';
import 'package:solar_rooftop/home/<USER>';
import 'package:solar_rooftop/login/login_service.dart';
import 'package:solar_rooftop/otp/otp_controller.dart';
import 'package:solar_rooftop/utils/api_handler.dart';
import 'package:solar_rooftop/utils/constants.dart';
import 'user_model.dart';

final loginController =
    ChangeNotifierProvider<LoginProvider>((ref) => LoginProvider());
LoginService loginService = LoginService();

class LoginProvider extends ChangeNotifier {
  final LoginService _loginService = LoginService();
  final UserModel _loggedUser = UserModel(
      profileName: '',
      dashboardId: '',
      password: '',
      mobileNumber: '',
      customerId: '',
      label: '',
      userId: '',
      email: '');
  int _otp = 0;

  textFieldUpdate(field, value) {
    if (field == 'mobileNumber') {
      _loggedUser.mobileNumber = value;
    } else if (field == 'email') {
      _loggedUser.email = value;
    } else if (field == 'profileName') {
      _loggedUser.profileName = value;
    }
    notifyListeners();
  }

  updateUserVerifiedStatus(value) async {
    final prefs = await SharedPreferences.getInstance();
    _loggedUser.isOtpVerified = value;
    await prefs.setString('userDetails', jsonEncode((_loggedUser.toJson())));
  }

  setCurrentUserDetails(userjson, token, refreshToken) {
    _loggedUser.userId = userjson['userId'];
    _loggedUser.token = token;
    _loggedUser.refreshToken = refreshToken;
    _loggedUser.customerId = userjson['customerId'];
    _loggedUser.profileName = userjson['profileName'];
    _loggedUser.mobileNumber = userjson['mobileNumber'];
    _loggedUser.dashboardId = userjson['dashboardId'];
    _loggedUser.email = userjson['email'];
    _loggedUser.userType = null;
    log('loggedUSer $_loggedUser');
    notifyListeners();
  }

  Future login(WidgetRef ref, [isOtpVerified = false]) async {
    final prefs = await SharedPreferences.getInstance();

    try {
      Response response =
          await _loginService.thingsboardLogin(_loggedUser.mobileNumber ?? '');

      if (response.statusCode == 200) {
        _loggedUser.profileName = response.data['firstName'];
        _loggedUser.email = response.data['email'];
       _loggedUser.dashboardId = response.data['dashboardId'];
        _loggedUser.token = response.data['token'];
        _loggedUser.isOtpVerified = isOtpVerified;
        _loggedUser.refreshToken = response.data['refreshToken'];
        await prefs.setString(
            'userDetails', jsonEncode((_loggedUser.toJson())));
        setCurrentUserDetails(_loggedUser.toJson(), response.data['token'],
            response.data['refreshToken']);
        notifyListeners();
        return _loggedUser;
      } else {
        showToast(getErrorMessageFromThingsBoard(response.statusCode));
      }
    } catch (e) {
      print(e);
      showToast(getErrorMessageFromException(e));
    }
  }

  Future<void> loginProcess(
      BuildContext context, WidgetRef ref, String mobileNumber) async {
    await ref.read(loginController).login(ref).then((value) async {
      if (context.mounted) {
        if (value != null) {
          ref.read(otpController).getOTP(context, ref, mobileNumber);
        } else {
          cleanLoaderMessage(context, ref);
        }
      }
    });
  }

  void cleanData() async {
    final prefs = await SharedPreferences.getInstance();
    prefs.setString('token', '');
    prefs.setString('refreshToken', '');
    prefs.setString('userDetails', '');
    _loggedUser.profileName = '';
    _loggedUser.mobileNumber = '';
    _loggedUser.email = '';
    _loggedUser.userId = null;
     _loggedUser.dashboardId = '';
    _loggedUser.customerId = null;
    _loggedUser.label = null;
    _loggedUser.password = null;
    _loggedUser.userType = null;
    _loggedUser.isOtpVerified = null;
    _loggedUser.token = null;
    _loggedUser.refreshToken = null;
    _loggedUser.createdBy = null;
    _loggedUser.createdDate = null;
    _loggedUser.updatedBy = null;
    _loggedUser.updatedDate = null;
  }

  Future logout(
    BuildContext context,
    WidgetRef ref,
    String? gwId,
  ) async {
    setLoaderMessage(context, ref, logingOut);
    if (context.mounted) {
      try {
        cleanData();
        ref.read(otpController).cleanOtpData();
        if (context.mounted) ref.read(otpController).logoutFirebase(context);
        return 200;
      } catch (e) {
        Navigator.of(context, rootNavigator: true).pop();
        log('error $e');
        showToast(getErrorMessageFromException(e));
      } finally {
        if (context.mounted) cleanLoaderMessage(context, ref);
      }
    }
  }

  int get otp => _otp;
  UserModel get loggedUser => _loggedUser;
}
