class UserModel {
  String? profileName;
  String? mobileNumber;
  String? email;
  String? userId;
  String? customerId;
  String? label;
  String? dashboardId;
  String? password;
  String? userType;
  bool? isOtpVerified;
  String? token;
  String? refreshToken;
  String? createdBy;
  String? createdDate;
  String? updatedBy;
  String? updatedDate;

  UserModel(
      {this.profileName,
      this.mobileNumber,
      this.email,
      this.userId,
      this.customerId,
      this.label,
      this.dashboardId,
      this.password,
      this.userType,
      this.isOtpVerified,
      this.token,
      this.refreshToken,
      this.createdBy,
      this.createdDate,
      this.updatedBy,
      this.updatedDate});

  UserModel.fromJson(Map<String, dynamic> json) {
    profileName = json['profileName'].toString();
    mobileNumber = json['mobileNumber'].toString();
    email = json['email'].toString();
    userId = json['userId'].toString();
    dashboardId = json['dashboardId'].toString();
    customerId = json['customerId'].toString();
    label = json['label'].toString();
    password = json['password'].toString();
    userType = json['userType'];
    isOtpVerified = json['isOtpVerified'];
    token = json['userToken'];
    refreshToken = json['refreshToken'];
    createdBy = json['createdBy'];
    createdDate = json['createdDate'];
    updatedBy = json['updatedBy'];
    updatedDate = json['updatedDate'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['profileName'] = profileName;
    data['mobileNumber'] = mobileNumber;
    data['email'] = email;
    data['userId'] = userId;
    data['customerId'] = customerId;
    data['dashboardId'] = dashboardId;
    data['label'] = label;
    data['password'] = password;
    data['userType'] = userType;
    data['isOtpVerified'] = isOtpVerified;
    data['userToken'] = token;
    data['refreshToken'] = refreshToken;
    data['createdBy'] = createdBy;
    data['createdDate'] = createdDate;
    data['updatedBy'] = updatedBy;
    data['updatedDate'] = updatedDate;
    return data;
  }
}
