import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:solar_rooftop/utils/app_config.dart';

class LoginService {
  Future thingsboardLogin(String mobileNumber) async {
    final prefs = await SharedPreferences.getInstance();
    try {
      var dio = Dio();
      var data = FormData.fromMap({'phone': mobileNumber});
      var response = await dio.post(AppConfig.api.login,
          queryParameters: {'phone': mobileNumber}, data: data);
      if (response.statusCode == 200 && response.data['token'] != null) {
        prefs.setString('token', response.data['token']);
        log("token -> ${response.data['token']}");
        prefs.setString('refreshToken', response.data['refreshToken']);
      }
      return response;
    } catch (error) {
      print(error);
      rethrow;
    }
  }
}
