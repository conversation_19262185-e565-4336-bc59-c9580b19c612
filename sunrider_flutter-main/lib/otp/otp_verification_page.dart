import 'package:connectivity_widget/connectivity_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:solar_rooftop/home/<USER>';
import 'package:solar_rooftop/no_internet_pages/no_internet_page.dart';
import 'package:solar_rooftop/otp/otp_controller.dart';
import 'package:solar_rooftop/utils/api_handler.dart';
import 'package:solar_rooftop/utils/common_widgets.dart';
import 'package:solar_rooftop/utils/dialog_box.dart';
import '../login/login_controller.dart';
import '../login/user_model.dart';
import '../utils/constants.dart';

class OtpPageVerification extends ConsumerWidget {
  static final GlobalKey<FormState> _key = GlobalKey();

  final scaffoldKey = GlobalKey<ScaffoldState>();
  TextEditingController controller = TextEditingController();
  OtpPageVerification({super.key});
  final Color primaryColor = const Color.fromARGB(255, 5, 77, 67);

  @override
  Widget build(
    BuildContext context,
    WidgetRef ref,
  ) {
    UserModel loggedInUser = ref.watch(loginController).loggedUser;

    bool showOtp = ref.watch(otpController).showResendOtp;
    return WillPopScope(
      onWillPop: () async {
        if (!showOtp) {
          return true;
        } else {
          bool canPop = await showExitForVerificationPage(context, ref);
          return canPop;
        }
      },
      child: SafeArea(
        child: GestureDetector(
          onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
          child: Scaffold(
            key: scaffoldKey,
            body: Form(key: _key, child: _body(context, ref, loggedInUser)),
          ),
        ),
      ),
    );
  }

  _body(
    BuildContext context,
    WidgetRef ref,
    UserModel loggedInUser,
  ) {
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;
    String enteredOtp = ref.watch(otpController).enteredOtp;
    String mobileNumber =
        ref.watch(loginController).loggedUser.mobileNumber ?? '';
    String modifiedNumber = "X" * 5 + mobileNumber.substring(5);

    return ConnectivityWidget(
        offlineBanner: const NoInternetConnectivityToast(),
        builder: (BuildContext context, bool isOnline) {
          return SingleChildScrollView(
            child: SizedBox(
              height: height,
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        //          Padding(
                        //   padding: const EdgeInsets.only(top: 40),
                        //   child: SizedBox(
                        //       height: height / 4,
                        //       child: Image.asset(
                        //         solarLogo,
                        //         fit: BoxFit.fill,
                        //       )),
                        // ),

                        SizedBox(
                          height: height / 4,
                          width: width,
                          child: SvgPicture.asset(
                            schnellLogo,
                            alignment: Alignment.center,
                            color: Theme.of(context).primaryColor,
                            fit: BoxFit.cover,
                          ),
                        ),
                        Center(
                          child: Padding(
                            padding: EdgeInsets.fromLTRB(20, 20, 20, 10),
                            child: Text(
                              "Enter OTP send to your number",
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Theme.of(context).secondaryHeaderColor,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),
                        Text(
                          modifiedNumber,
                          style: TextStyle(
                              color: Theme.of(context).primaryColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 15),
                        ),
                        SizedBox(
                          width: width > 1000 ? 600 : width,
                          child: Padding(
                              padding: const EdgeInsets.fromLTRB(30, 20, 30, 0),
                              child: PinCodeTextField(
                                controller: controller,
                                length: 6,
                                obscureText: false,
                                animationType: AnimationType.fade,
                                keyboardType: TextInputType.number,
                                textStyle: TextStyle(
                                  color: Theme.of(context).secondaryHeaderColor
                                ),
                                pinTheme: PinTheme(
                                    shape: PinCodeFieldShape.box,
                                    borderRadius: BorderRadius.circular(5),
                                    fieldHeight: width > 1000 ? 80 : 50,
                                    fieldWidth: width > 1000 ? 70 : 40,
                                    activeFillColor: Colors.grey[200],
                                    inActiveBoxShadow: [
                                      BoxShadow(
                                        color: Colors.grey.withOpacity(0.5),
                                        spreadRadius: 2,
                                        blurRadius: 4,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                    
                                    activeColor: Colors.grey,
                                    selectedFillColor: const Color.fromARGB(
                                        127, 158, 158, 158),
                                    selectedColor: const Color.fromARGB(
                                        127, 158, 158, 158),
                                    inactiveFillColor: Colors.white,
                                    inactiveColor: Colors.grey),
                                animationDuration:
                                    const Duration(milliseconds: 300),
                                enableActiveFill: true,
                                inputFormatters: <TextInputFormatter>[
                                  FilteringTextInputFormatter.allow(
                                      RegExp(r'[0-9]')),
                                ],
                                autoDisposeControllers: false,
                                onChanged: (value) {
                                  ref.read(otpController).otpFieldUpdate(value);
                                },
                                beforeTextPaste: (text) {
                                  return true;
                                },
                                appContext: context,
                              )),
                        ),
                        CustomButton(
                          label: "Verify",
                          horizontalPadding: width > 1000 ? 80 : 20,
                          verticalPadding: width > 1000 ? 30 : 10,
                          onPressed: () {
                            FocusScope.of(context).requestFocus(FocusNode());
                            if (enteredOtp.isNotEmpty) {
                              setLoaderMessage(context, ref, validatingOtp);

                              ref
                                  .read(otpController)
                                  .loginWithOtp(
                                      ref.read(otpController).enteredOtp)
                                  .then((value) async {
                                if (value == 200) {
                                  if (context.mounted) {
                                    ref
                                        .read(loginController)
                                        .updateUserVerifiedStatus(true);
                                    Navigator.of(context)
                                        .pushNamedAndRemoveUntil(
                                            homeRoute, (route) => false);
                                  }

                                  return;
                                } else {
                                  if (context.mounted) {
                                    cleanLoaderMessage(context, ref);
                                  }
                                  showToast(invalidOtpMessage);
                                }
                              });
                            } else {
                              showToast(otpRequired);
                            }
                          },
                        ),
                        Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 30, vertical: 10),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                if (ref.watch(otpController).showResendOtp)
                                  Padding(
                                    padding: const EdgeInsets.only(right: 10),
                                    child: Text(
                                      "Resend OTP in ${ref.watch(otpController).secondsLeft} seconds",
                                      style: TextStyle(
                                        color: Theme.of(context)
                                            .secondaryHeaderColor,
                                      ),
                                    ),
                                  ),
                                Padding(
                                  padding: const EdgeInsets.only(top: 10),
                                  child:
                                      (!ref.watch(otpController).showResendOtp)
                                          ? GestureDetector(
                                              onTap: () {
                                                controller.clear();
                                                ref
                                                    .read(otpController)
                                                    .resendSMS(context);
                                              },
                                              child: Text(
                                                'Resend OTP',
                                                style: TextStyle(
                                                  color: Theme.of(context)
                                                      .primaryColor,
                                                  decoration:
                                                      TextDecoration.underline,
                                                ),
                                              ),
                                            )
                                          : null,
                                ),
                              ],
                            )),
                      ],
                    ),
                    const SizedBox(
                      height: 80,
                    )
                  ]),
            ),
          );
        });
  }
}
