import 'package:connectivity_widget/connectivity_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:solar_rooftop/no_internet_pages/no_internet_page.dart';
import 'package:solar_rooftop/otp/otp_controller.dart';
import 'package:solar_rooftop/utils/app_config.dart';
import 'package:solar_rooftop/utils/common_widgets.dart';
import 'package:solar_rooftop/utils/constants.dart';
import 'package:solar_rooftop/utils/dialog_box.dart';
import '../login/login_controller.dart';
import '../login/user_model.dart';

class OTPPage extends ConsumerWidget {
  static final GlobalKey<FormState> _key = GlobalKey();

  final scaffoldKey = GlobalKey<ScaffoldState>();

  OTPPage({super.key});
  final Color primaryColor = const Color.fromARGB(255, 5, 77, 67);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    UserModel loginUser = ref.watch(loginController).loggedUser;

    return WillPopScope(
      onWillPop: () => showExitPopup(context),
      child: SafeArea(
        child: GestureDetector(
          onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
          child: Scaffold(
            key: scaffoldKey,
            body: Form(key: _key, child: _body(context, ref, loginUser)),
          ),
        ),
      ),
    );
  }

  _body(
    BuildContext context,
    WidgetRef ref,
    UserModel loginUser,
  ) {
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;
    return ConnectivityWidget(
      offlineBanner: const NoInternetConnectivityToast(),
      builder: (BuildContext context, bool isOnline) {
        return SingleChildScrollView(
          child: SizedBox(
            height: height / 1.1,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                const Spacer(),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: height / 4,
                      width: width,
                      child: SvgPicture.asset(
                        schnellLogo,
                        alignment: Alignment.center,
                        color: Theme.of(context).primaryColor,
                        fit: BoxFit.cover,
                      ),
                    ),
                    Center(
                      child: Padding(
                        padding: const EdgeInsets.only(top: 30),
                        child: Text(
                          "Sign In",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Theme.of(context).primaryColor,
                            fontSize: 18,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 30.0),
                    Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: width > 1000 ? 80 : 20),
                      child: _mobileNumber(context, ref, loginUser),
                    ),
                    CustomButton(
                      label: 'Send OTP',
                      icon: Icons.verified_user_sharp,
                      horizontalPadding: width > 1000 ? 80 : 20,
                      verticalPadding: width > 1000 ? 30 : 20,
                      onPressed: () {
                        FocusScope.of(context).requestFocus(FocusNode());
                        if (_key.currentState!.validate()) {
                          _key.currentState!.save();

                          ref
                              .read(otpController)
                              .getOtpForOldUser(context, ref, loginUser);
                        }
                      },
                    ),
                  ],
                ),
                const Spacer(),
                if(AppConfig.currentEnvironment == Environment.dev)
                Text(
                  'v$appVersion - Staging',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  _mobileNumber(BuildContext context, WidgetRef ref, UserModel loginUser) {
    TextEditingController controller = TextEditingController();
    controller.text = ref.watch(loginController).loggedUser.mobileNumber ?? '';
    controller.selection = TextSelection.fromPosition(
        TextPosition(offset: controller.text.length));
    return TextFormField(
      maxLength: 13,
      cursorColor: Theme.of(context).primaryColor,
      onChanged: (value) {
        ref.read(loginController).textFieldUpdate('mobileNumber', value);
      },
      controller: controller,
      decoration: InputDecoration(
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25.0),
            borderSide: BorderSide(
              color: Theme.of(context).primaryColor,
            ),
          ),
          contentPadding: const EdgeInsets.all(16.0),
          hintText: 'Mobile Number',
          counterText: "",
          suffixIcon: Padding(
            padding: const EdgeInsets.only(right: 20.0),
            child: Icon(
              Icons.call,
              size: 25,
              color: Theme.of(context).primaryColor,
            ),
          ),
          hintStyle: Theme.of(context).textTheme.titleLarge!.copyWith(
              fontSize: 15.0, color: const Color.fromARGB(248, 183, 184, 185)),
          border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(30.0),
              borderSide: BorderSide(color: primaryColor)),
          filled: true,
          counterStyle:
              TextStyle(color: Theme.of(context).secondaryHeaderColor),
          fillColor: Colors.white),
      style: Theme.of(context).textTheme.bodySmall!.copyWith(fontSize: 16.0),
      keyboardType: TextInputType.number,
    );
  }
}
