import 'dart:async';
import 'dart:core';
import 'dart:developer';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:solar_rooftop/home/<USER>';
import 'package:solar_rooftop/login/login_controller.dart';
import 'package:solar_rooftop/utils/common_utils.dart';
import 'package:solar_rooftop/utils/constants.dart';
import '../login/user_model.dart';
import '../utils/api_handler.dart';

final otpController =
    ChangeNotifierProvider<OtpProvider>((ref) => OtpProvider());

class OtpProvider extends ChangeNotifier {
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  String verifyId = "";
  String verifyIdForNewNumber = "";
  String _enteredOtp = '';
  String phoneNumber = '';
  bool _isOtpForNewNumber = false;
  int? token = 0;
  int? tokenForNewNumber = 0;
  bool showResendOtp = true;
  int secondsLeft = 0;
  late Timer timerInstance;

  void otpFieldUpdate(otp) {
    _enteredOtp = otp;
    notifyListeners();
  }

  void cleanOtpData() {
    verifyId = "";
    verifyIdForNewNumber = "";
    _isOtpForNewNumber = false;
    tokenForNewNumber = 0;
    _enteredOtp = '';
    phoneNumber = '';
    token = 0;
    showResendOtp = true;
    secondsLeft = 0;
    notifyListeners();
  }

  startResendOtpTimer() {
    showResendOtp = true;
    startTimer(30);
    notifyListeners();
  }

  startTimer(int seconds) {
    secondsLeft = seconds;
    timerInstance = Timer.periodic(const Duration(seconds: 1), (Timer timer) {
      if (secondsLeft == 0) {
        showResendOtp = false;
        timer.cancel();
      } else {
        secondsLeft--;
      }
      notifyListeners();
    });
  }

  @override
  void dispose() {
    showResendOtp = false;
    timerInstance.cancel();
    super.dispose();
  }

  void resetTimer() {
    secondsLeft = 0;
    showResendOtp = false;
    timerInstance.cancel();
    notifyListeners();
  }

  getOtpForOldUser(BuildContext context, WidgetRef ref, UserModel loginUser) {
    if (!isValidNumber(
        ref.read(loginController).loggedUser.mobileNumber ?? '')) {
      showToast(provideValidNumber);
      return;
    }
    setLoaderMessage(context, ref, sendingOtp);
    ref
        .read(loginController)
        .loginProcess(context, ref, loginUser.mobileNumber!);
  }

  Future getOTP(BuildContext context, WidgetRef ref, String phone,
      [bool isFromChangeSim = false, bool isOtpForNewNumber = false]) async {
    phoneNumber = phone;
    _isOtpForNewNumber = isOtpForNewNumber;
    await _firebaseAuth
        .verifyPhoneNumber(
      timeout: const Duration(seconds: 30),
      phoneNumber: "+91$phoneNumber",
      verificationCompleted: (phoneAuthCredential) async {
        return;
      },
      verificationFailed: (error) async {
        print(error);
        if (error.code == 'invalid-phone-number') {
          cleanLoaderMessage(context, ref);
          Fluttertoast.showToast(
            msg: 'Invalid Phone Number',
            toastLength: Toast.LENGTH_SHORT,
          );
        } else {
          cleanLoaderMessage(context, ref);
          if (isOtpForNewNumber) {
            Navigator.of(context, rootNavigator: true).pop();
          }
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
            content: Text(
              errorSendingOtp,
              style: TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.red,
          ));
        }
        return;
      },
      codeSent: (verificationId, forceResendingToken) async {
        if (_isOtpForNewNumber) {
          verifyIdForNewNumber = verificationId;
          tokenForNewNumber = forceResendingToken;
        } else {
          verifyId = verificationId;
          token = forceResendingToken;
        }
        startResendOtpTimer();
        log('**** code send');
        ref.read(otpController).otpFieldUpdate('');
        cleanLoaderMessage(context, ref);
        //We need to remove the below once the login API was completed
        //_loginService.loginService('');
        if (!isFromChangeSim) {
          Navigator.of(context).pushNamed(otpVerificationRoute);
        } else if (!isOtpForNewNumber) {
          // getOtpForChangeSimNumber(ref, context, phoneNumber);

          Navigator.of(context, rootNavigator: true).pop();
        }
      },
      codeAutoRetrievalTimeout: (verificationId) async {
        return;
      },
    )
        .onError((error, stackTrace) {
      print(error);
      cleanLoaderMessage(context, ref);
      if (isOtpForNewNumber) {
        Navigator.of(context, rootNavigator: true).pop();
      }
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
        content: Text(
          errorSendingOtp,
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.red,
      ));
    });
  }

  Future<void> resendSMS(BuildContext context) async {
    _enteredOtp = '';
    notifyListeners();
    startResendOtpTimer();
    int tokenStored = 0;
    if (_isOtpForNewNumber) {
      tokenStored = tokenForNewNumber ?? 0;
    } else {
      tokenStored = token ?? 0;
    }
    await FirebaseAuth.instance
        .verifyPhoneNumber(
      phoneNumber: "+91$phoneNumber",
      verificationCompleted: (PhoneAuthCredential credential) {},
      verificationFailed: (FirebaseAuthException e) {
         print(e);
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
          content: Text(
            errorSendingOtp,
            style: TextStyle(color: Colors.white),
          ),
          backgroundColor: Colors.red,
        ));
      },
      codeSent: (String verificationId, int? forceResendingToken) {},
      codeAutoRetrievalTimeout: (String verificationId) {},
      forceResendingToken: tokenStored,
    )
        .onError((error, stackTrace) {
      log('error on ->  $error');
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
        content: Text(
          errorSendingOtp,
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.red,
      ));
    });
  }

  Future loginWithOtp(String enteredOtp) async {
    String verificationId = '';
    if (_isOtpForNewNumber) {
      verificationId = verifyIdForNewNumber;
    } else {
      verificationId = verifyId;
    }
    final cred = PhoneAuthProvider.credential(
        verificationId: verificationId, smsCode: enteredOtp);

    try {
      final user = await _firebaseAuth.signInWithCredential(cred);
      if (user.user != null) {
        return 200;
      } else {
        return "Error in Otp login";
      }
    } on FirebaseAuthException catch (e) {
      return e.message.toString();
    } catch (e) {
      return e.toString();
    }
  }

  Future logoutFirebase(BuildContext context) async {
    await _firebaseAuth.signOut().then((value) => Navigator.of(context)
        .pushNamedAndRemoveUntil(otpRoute, (route) => false));
  }

  Future<bool> isLoggedIn() async {
    var user = _firebaseAuth.currentUser;
    return user != null;
  }

  String get enteredOtp => _enteredOtp;
  bool get isOtpForNewNumber => _isOtpForNewNumber;
}
