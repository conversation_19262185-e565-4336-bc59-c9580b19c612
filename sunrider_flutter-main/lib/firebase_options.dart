// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBOVa2jxc48tjBMkPW5A-amKHMahJgoZ6A',
    appId: '1:231954768472:ios:6e761ca38b6631ac1ea02d',
    messagingSenderId: '231954768472',
    projectId: 'solar-rooftop-a7cfc',
    storageBucket: 'solar-rooftop-a7cfc.appspot.com',
    iosBundleId: 'com.schnell.solarRooftop',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBozBEtrsgPbvd3v1gz0XVYbpOIl3KK8lU',
    appId: '1:231954768472:android:4dcbe050332476f91ea02d',
    messagingSenderId: '231954768472',
    projectId: 'solar-rooftop-a7cfc',
    storageBucket: 'solar-rooftop-a7cfc.appspot.com',
  );
}