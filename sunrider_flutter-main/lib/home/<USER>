import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:connectivity_widget/connectivity_widget.dart';
import 'package:solar_rooftop/login/login_controller.dart';
import 'package:solar_rooftop/utils/app_config.dart';
import 'package:solar_rooftop/utils/widget_action_handler.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:solar_rooftop/no_internet_pages/no_internet_page.dart';
import 'package:solar_rooftop/utils/custom_appbar.dart';
import 'package:solar_rooftop/utils/dialog_box.dart';
import 'package:url_launcher/url_launcher.dart';

final webViewKey = GlobalKey();

InAppWebViewGroupOptions options = InAppWebViewGroupOptions(
  crossPlatform: InAppWebViewOptions(
    useShouldOverrideUrlLoading: true,
    mediaPlaybackRequiresUserGesture: false,
    javaScriptEnabled: true,
    cacheEnabled: true,
    supportZoom: false,
  ),
  android: AndroidInAppWebViewOptions(
    useHybridComposition: true,
    thirdPartyCookiesEnabled: true,
  ),
  ios: IOSInAppWebViewOptions(
    allowsInlineMediaPlayback: true,
    allowsBackForwardNavigationGestures: false,
  ),
);

class HomePage extends ConsumerWidget {
  const HomePage({super.key});

  Future<Map<String, String?>> getToken(WidgetRef ref) async {
    final loginState = ref.watch(loginController);
    String? accessToken = loginState.loggedUser.token;
    String? refreshToken = loginState.loggedUser.refreshToken;
     String? dashboardId = loginState.loggedUser.dashboardId;
    print(
        '${AppConfig.thingsBoardUrl}/dashboard/$dashboardId?accessToken=$accessToken&refreshToken=$refreshToken');
    return {'accessToken': accessToken, 'refreshToken': refreshToken};
  }



  @override
  Widget build(BuildContext context, WidgetRef ref) {
    String? dashboardId = ref.watch(loginController).loggedUser.dashboardId;
    return WillPopScope(
      onWillPop: () => showExitPopup(context),
      child: SafeArea(
        child: ConnectivityWidget(
          offlineBanner: const NoInternetConnectivityToast(),
          builder: (BuildContext context, bool isOnline) {
            return Scaffold(
              appBar: CustomAppBar(
                height: 55,
                child: HomeGuardAppBar(
                  callback: () {},
                ),
              ),
              body: FutureBuilder<Map<String, String?>>(
                future: getToken(ref),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (snapshot.hasError) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (!snapshot.hasData ||
                      snapshot.data!['accessToken'] == null) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  final accessToken = snapshot.data!['accessToken'];
                  final refreshToken = snapshot.data!['refreshToken'];

                  return Padding(
                    padding: const EdgeInsets.only(top: 18.0),
                    child: InAppWebView(
                      key: webViewKey,
                      initialUrlRequest: URLRequest(
                        url: Uri.parse(
                            '${AppConfig.thingsBoardUrl}/dashboard/$dashboardId?accessToken=$accessToken&refreshToken=$refreshToken')),
                      initialOptions: options,
                     onWebViewCreated: (webViewController) {
                          log("onWebViewCreated");
                          webViewController.addJavaScriptHandler(
                              handlerName: "tbMobileDashboardLoadedHandler",
                              callback: (args) async {
                                bool hasRightLayout = args[0];
                                bool rightLayoutOpened = args[1];
                                log(
                                    "Invoked tbMobileDashboardLoadedHandler: hasRightLayout: $hasRightLayout, rightLayoutOpened: $rightLayoutOpened");
                                
                              });
                          webViewController.addJavaScriptHandler(
                              handlerName: "tbMobileDashboardLayoutHandler",
                              callback: (args) async {
                                bool rightLayoutOpened = args[0];
                                log(
                                    "Invoked tbMobileDashboardLayoutHandler: rightLayoutOpened: $rightLayoutOpened");
                              
                              });
                          webViewController.addJavaScriptHandler(
                              handlerName: "tbMobileDashboardStateNameHandler",
                              callback: (args) async {
                                log(
                                    "Invoked tbMobileDashboardStateNameHandler: $args");
                                if (args.isNotEmpty && args[0] is String) {
                                  // if (widget._titleCallback != null) {
                                  //   widget._titleCallback!(args[0]);
                                  // }
                                }
                              });
                          webViewController.addJavaScriptHandler(
                              handlerName: "tbMobileNavigationHandler",
                              callback: (args) async {
                                log(
                                    "Invoked tbMobileNavigationHandler: $args");
                                if (args.length > 0) {
                                  String? path = args[0];
                                  Map<String, dynamic>? params;
                                  if (args.length > 1) {
                                    params = args[1];
                                  }
                                  log("path: $path");
                                  log("params: $params");
                                  
                                }
                              });
                          webViewController.addJavaScriptHandler(
                              handlerName: "tbMobileHandler",
                              callback: (args) async {
                                log("Invoked tbMobileHandler: $args");
                                return await WidgetActionHandler
                                    .handleWidgetMobileAction(
                                        args, webViewController);
                              });
                        },
                        shouldOverrideUrlLoading:
                            (controller, navigationAction) async {
                          var uri = navigationAction.request.url!;
                          var uriString = uri.toString();
                          log('shouldOverrideUrlLoading $uriString');
                          if (
                                  navigationAction.iosWKNavigationType ==
                                      IOSWKNavigationType.LINK_ACTIVATED) {
                            if (uriString.startsWith(AppConfig.thingsBoardUrl)) {
                              var target = uriString.substring(
                                  AppConfig.thingsBoardUrl.length);
                              if (!target.startsWith("?accessToken")) {
                                if (target.startsWith("/")) {
                                  target = target.substring(1);
                                }
                                
                                return NavigationActionPolicy.CANCEL;
                              }
                            } 
                          }
                        return  NavigationActionPolicy.ALLOW;
                       
                      
                       
                      },
                      onUpdateVisitedHistory:
                          (controller, url, androidIsReload) async {
                        log('onUpdateVisitedHistory: $url');
                      },
                      onConsoleMessage: (controller, consoleMessage) {
                        log(
                            '[JavaScript console] ${consoleMessage.messageLevel}: ${consoleMessage.message}');
                      },
                      onLoadStart: (controller, url) async {
                        log('onLoadStart: $url');
                      },
                      onLoadStop: (controller, url) async {
                        log('onLoadStop: $url');
                        
                      },
                      onLoadError: (controller, url, code, message) {
                        log('onLoadError: $url, code: $code, message: $message');
                      },
                      androidOnPermissionRequest:
                          (controller, origin, resources) async {
                        log(
                            'androidOnPermissionRequest origin: $origin, resources: $resources');
                        return PermissionRequestResponse(
                            resources: resources,
                            action: PermissionRequestResponseAction.GRANT);
                      },
                    ),
                  );
                },
              ),
            );
          },
        ),
      ),
    );
  }
}
