import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:in_app_update/in_app_update.dart';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:solar_rooftop/login/login_controller.dart';
import 'package:solar_rooftop/login/login_service.dart';
import 'package:solar_rooftop/otp/otp_controller.dart';
import 'package:solar_rooftop/utils/api_handler.dart';
import 'package:solar_rooftop/utils/constants.dart';

final splashController =
    ChangeNotifierProvider<SplashProvider>((ref) => SplashProvider());

class SplashProvider extends ChangeNotifier {
  late Timer timer;
  bool checked = true;
  Future<bool> checkAppVersion(BuildContext context, WidgetRef ref) async {
    if (checked) {
      try {
        var info = await InAppUpdate.checkForUpdate();
        debugPrint('Update availability: ${info.updateAvailability}');

        if (info.updateAvailability == UpdateAvailability.updateAvailable) {
          if (context.mounted) {
            Navigator.pushNamedAndRemoveUntil(
                context, updateRoute, (Route<dynamic> route) => false);
          }
        } else {
          if (context.mounted) {
            _init(context, ref);
            return Future.value(true);
          }
        }
      } catch (e) {
        debugPrint('Error checking for update: $e');
        showToast('Error while launching');
      }
    }
    return Future.value(false);
  }

  _init(BuildContext context, WidgetRef ref) {
    Timer(const Duration(milliseconds: 1000), () async {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString("token") ?? '';
      String? refreshToken = prefs.getString("refreshToken") ?? '';

      String? userDetails = prefs.getString("userDetails") ?? '';

      log('token $token');
      log('refreshToken $refreshToken');

      log('userDetails -->  $userDetails');
      try {
        if (userDetails.isNotEmpty &&
            jsonDecode(userDetails)['mobileNumber'] != null) {
          Response response = await LoginService()
              .thingsboardLogin(jsonDecode(userDetails)['mobileNumber']);
          if (response.statusCode == 200) {
            if (token.isNotEmpty &&
                refreshToken.isNotEmpty &&
                (jsonDecode(userDetails)['isOtpVerified'] == true)) {
              ref.read(loginController).setCurrentUserDetails(
                  jsonDecode(userDetails), token, refreshToken);

              await ref
                  .read(loginController)
                  .login(ref, true)
                  .then((value) async {
                if (context.mounted) {
                  if (value != null) {
                    Navigator.of(context)
                        .pushNamedAndRemoveUntil(homeRoute, (route) => false);
                  } else {
                    ref.read(loginController).cleanData();
                    ref.read(otpController).cleanOtpData();

                    if (context.mounted) {
                      Navigator.of(context)
                          .pushNamedAndRemoveUntil(otpRoute, (route) => false);
                    }
                  }
                }
              });
            } else {
              ref.read(loginController).cleanData();
              ref.read(otpController).cleanOtpData();

              if (context.mounted) {
                Navigator.of(context)
                    .pushNamedAndRemoveUntil(otpRoute, (route) => false);
              }
            }
          } else {
            showToast('Error while launching');
          }
        } else {
          ref.read(loginController).cleanData();
          ref.read(otpController).cleanOtpData();

          if (context.mounted) {
            Navigator.of(context)
                .pushNamedAndRemoveUntil(otpRoute, (route) => false);
          }
        }
      } catch (e) {
        showToast('Error while launching');
      }
    });
  }
}
