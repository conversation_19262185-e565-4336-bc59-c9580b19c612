import 'package:connectivity_widget/connectivity_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:solar_rooftop/no_internet_pages/no_internet_page.dart';
import 'package:solar_rooftop/utils/constants.dart';

import 'splash_controller.dart';

class SplashPage extends ConsumerWidget {
  const SplashPage({super.key});
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    double height = MediaQuery.of(context).size.height;
    return SafeArea(
      child: Scaffold(
        appBar: null,
        body: Padding(
          padding: EdgeInsets.zero,
          child: ConnectivityWidget(
            offlineBanner: const NoInternetConnectivityToast(),
            builder: (BuildContext context, bool isOnline) {
              return isOnline
                  ? FutureBuilder(
                      future: ref
                          .read(splashController)
                          .checkAppVersion(context, ref),
                      builder: (context, AsyncSnapshot<bool> snapshot) {
                        if (snapshot.connectionState == ConnectionState.done) {}
                        return SizedBox(
                          width: double.infinity,
                          height: height,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              const Spacer(),
                              Padding(
                                padding: const EdgeInsets.only(top: 0),
                                child: SizedBox(
                                  height: height / 2,
                                  child: SvgPicture.asset(
                                    schnellLogo,
                                    color: Theme.of(context).primaryColor,
                                    fit: BoxFit.contain,
                                  ),
                                ),
                              ),
                              const Spacer(),
                              Padding(
                                padding: const EdgeInsets.only(bottom :10.0),
                                child: Text(
                                  'v$appVersion',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    )
                  : const SizedBox();
            },
          ),
        ),
      ),
    );
  }
}
