import 'package:flutter/material.dart';
import 'package:solar_rooftop/home/<USER>';
import 'package:solar_rooftop/update/update_screen.dart';
import 'package:solar_rooftop/utils/constants.dart';

import 'otp/otp_page.dart';
import 'otp/otp_verification_page.dart';
import 'splash/splash_page.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

class SolarRoofTop extends StatelessWidget {
  const SolarRoofTop({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      navigatorKey: navigatorKey,
      debugShowCheckedModeBanner: false,
      title: 'Sunrider',
      theme: lightTheme(context),
      initialRoute: '/',
      routes: {
        splashRoute: (context) => const SplashPage(),
        loginRoute: (context) =>  OTPPage(),
        otpRoute: (context) => OTPPage(),
        homeRoute: (context) =>   HomePage(),
        otpVerificationRoute: (context) => OtpPageVerification(),
        updateRoute : (context) => const UpdatePage(),
      },
    );
  }
}
