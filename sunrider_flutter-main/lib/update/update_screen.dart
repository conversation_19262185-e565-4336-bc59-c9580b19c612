import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:solar_rooftop/utils/constants.dart';
import 'package:url_launcher/url_launcher.dart';

class UpdatePage extends ConsumerWidget {
  const UpdatePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SafeArea(
      child: Scaffold(
        body: _formUI(context, ref),
      ),
    );
  }

  _formUI(BuildContext context, WidgetRef ref) {
    double height = MediaQuery.of(context).size.height;
    double width = MediaQuery.of(context).size.width;
    return SizedBox(
        height: height / 1.3,
        child: Column(children: <Widget>[
          const Spacer(),
          SizedBox(
            height: height / 5,
            width: width,
            child: SvgPicture.asset(
              schnellLogo,
              alignment: Alignment.center,
              color: Theme.of(context).primaryColor,
              fit: BoxFit.cover,
            ),
          ),
          SizedBox(height: height / 12),
          Align(
            alignment: Alignment.center,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 30),
              child: Text(
                'Kindly upgrade to latest version from Play Store to continue using this app!',
                textAlign: TextAlign.justify,
                style: TextStyle(
                    fontSize: 16, color: Theme.of(context).primaryColor),
              ),
            ),
          ),
          const SizedBox(height: 40.0),
          Container(
            padding: const EdgeInsets.fromLTRB(20, 10, 20, 40),
            child: SizedBox(
              height: MediaQuery.of(context).size.height / 17,
              width: double.infinity,
              child: ElevatedButton(
                style: ButtonStyle(
                  backgroundColor:
                      MaterialStateProperty.all(Theme.of(context).primaryColor),
                  foregroundColor: MaterialStateProperty.all(Colors.white),
                  shape: MaterialStateProperty.all(RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30.0))),
                ),
                child: const Text(
                  "Update",
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
                onPressed: () {
                  launchToAppLink();
                },
              ),
            ),
          ),
        ]));
  }

  Future<void> launchToAppLink() async {
    Uri url = Uri.parse(
        "https://play.google.com/store/apps/details?id=com.schnell.solar_rooftop");
    try {
      launchUrl(url);
    } on PlatformException {
      launchUrl(url);
    } finally {
      launchUrl(url);
    }
  }
}
