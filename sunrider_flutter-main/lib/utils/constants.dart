import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:google_fonts/google_fonts.dart';

//Constants

const appVersion = '1.0.1';
//EndPoints
const String auditMailServer = "";

const String tbURL = "https://salzerelectronics.in";
//Routes
const splashRoute = '/';
const otpRoute = '/login';
const loginRoute = '/scanOrLogin';
const homeRoute = '/home';
const otpVerificationRoute = '/otp';
const updateRoute = '/update';

//Images

const schnellLogo = 'assets/icons/sunrider-logo.svg';

//ErrorMessages
const invalidOtpMessage = "Invalid OTP. Please check and try again.";
const otpRequired = 'OTP Required';
const errorSendingOtp = "Error sending OTP, Please try again after some time.";

//popup

const logoutMessage = "Are you sure you wish to log out from Sunrider?";

// Warning messages
  final webViewKey = GlobalKey();
const sendingOtp =
    "Logging in with your mobile number. Please wait for an OTP...";
const registerProcess =
    "Registering your mobile number. Please wait for an OTP...";
const validatingOtp = "Verifying OTP. Please wait...";

const logingOut = "Logging out, Please wait...";
const provideValidNumber = "Please provide a valid 10 digit mobile number";

//Loaders
const spinkit = SpinKitCircle(
  color: Color.fromARGB(255, 4, 122, 54),
  size: 40.0,
);

ThemeData lightTheme(context) => ThemeData(
    useMaterial3: false,
    scaffoldBackgroundColor: Colors.white,
    textTheme:
        GoogleFonts.poppinsTextTheme(Theme.of(context).textTheme.copyWith(
              headlineSmall: TextStyle(color: Colors.grey[800]),
              titleLarge: TextStyle(color: Colors.grey[800]),
              bodyLarge: const TextStyle(color: Colors.black),
              bodySmall: const TextStyle(color:  Color(0xff505050)),
              titleMedium: const TextStyle(color: Colors.black),
              titleSmall: TextStyle(color: Colors.grey[200]),
              bodyMedium: const TextStyle(color: Colors.black),
            )),
    canvasColor: Colors.grey[400],
    primaryColor: const Color(0xffEC8D28),
    // primaryColor: const Color.fromARGB(255, 90, 139, 174),
    focusColor: Colors.amber[800],
    cardColor: Colors.red.shade800,
    highlightColor: Colors.greenAccent,
    secondaryHeaderColor:const Color(0xff505050),
    hoverColor: Colors.blueGrey,
    indicatorColor: Colors.pinkAccent[400],
    shadowColor: Colors.lightGreen[400],
    bottomAppBarTheme: BottomAppBarTheme(color: Colors.blueAccent[400]));

ThemeData darkTheme(context) => ThemeData(
      useMaterial3: false,
      scaffoldBackgroundColor: Colors.white,
      textTheme:
          GoogleFonts.poppinsTextTheme(Theme.of(context).textTheme.copyWith(
                headlineSmall: const TextStyle(color: Colors.grey),
                titleLarge: const TextStyle(color: Colors.grey),
                bodyLarge: const TextStyle(color: Colors.grey),
                bodySmall: const TextStyle(color: Colors.white),
                titleMedium: TextStyle(color: Colors.grey[400]),
                titleSmall: TextStyle(color: Colors.grey[200]),
                bodyMedium: const TextStyle(color: Colors.white),
              )),
      canvasColor: Colors.white,
      primaryColor: Colors.black,
      focusColor: Colors.amber[800],
      cardColor: Colors.red.shade800,
      highlightColor: Colors.blueGrey[400],
      secondaryHeaderColor: Colors.teal[800],
      hoverColor: Colors.blueGrey,
      indicatorColor: Colors.pinkAccent[400],
    );
