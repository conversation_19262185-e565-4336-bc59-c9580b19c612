import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

selectDeviceDialog(BuildContext context, WidgetRef ref, List deviceList,
    String title, Function action,
    [String dialogIsFor = '']) {
  showDialog(
    context: context,
    builder: (context) {
      return Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: SizedBox(
          width: MediaQuery.of(context).size.width * 0.88,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                ),
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Center(
                      child: Text(
                        title,
                        style:
                            const TextStyle(color: Colors.white, fontSize: 20),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height / 1.9,
                ),
                child: ListView.builder(
                  physics: const ClampingScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: deviceList.length,
                  itemBuilder: (BuildContext context, int index) {
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        GestureDetector(
                          onTap: () {
                            action(index);
                          },
                          child: SizedBox(
                            child: ListTile(
                              title: Center(
                                child: Text(
                                  '${deviceList[index].name.toString()} (${deviceList[index].label.toString()})',
                                  style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontSize: 16,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ),
                        ),
                        if (index == deviceList.length - 1 &&
                            dialogIsFor == 'changeGateway')
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              GestureDetector(
                                onTap: () {
                                 
                                },
                                child: Padding(
                                  padding: const EdgeInsets.only(
                                      right: 8.0, bottom: 8),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      shape: BoxShape.rectangle,
                                      color: Theme.of(context).primaryColor,
                                      border: Border.all(
                                        color: Colors.white,
                                        width: 2,
                                      ),
                                      borderRadius: BorderRadius.circular(10.0),
                                    ),
                                    child: const Padding(
                                      padding: EdgeInsets.all(8.0),
                                      child: Row(
                                        children: [
                                          Icon(
                                            Icons.add,
                                            color: Colors.white,
                                          ),
                                          Padding(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 8.0),
                                            child: Text(
                                              'Add',
                                              style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 15),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              )
                            ],
                          ),
                        (index != deviceList.length - 1)
                            ? const Divider(
                                thickness: 1,
                                color: Color.fromARGB(255, 150, 148, 148),
                              )
                            : const SizedBox(),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      );
    },
  );
}

dividerWithText(String value) {
  return Padding(
    padding: const EdgeInsets.symmetric(horizontal: 20),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          children: [
            const Expanded(
              child: Divider(
                color: Colors.grey,
                thickness: 1,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Text(
                value,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                  color: Colors.black,
                ),
              ),
            ),
            const Expanded(
              child: Divider(
                color: Colors.grey,
                thickness: 1,
              ),
            ),
          ],
        ),
      ],
    ),
  );
}

enum CustomButtonType { primaryButton, secondaryButton, disabledButton }

getCustomButtonColor(BuildContext context, CustomButtonType type) {
  switch (type) {
    case CustomButtonType.primaryButton:
      return Theme.of(context).primaryColor;
    case CustomButtonType.secondaryButton:
      return const Color.fromARGB(255, 146, 45, 45);
    case CustomButtonType.disabledButton:
      return Colors.grey;
  }
}

class CustomButton extends StatelessWidget {
  final String label;
  final Function onPressed;
  final IconData? icon;
  final CustomButtonType type;
  final bool disabled;
  final double horizontalPadding;
  final double verticalPadding;

  const CustomButton({
    super.key,
    required this.label,
    required this.onPressed,
    this.icon,
    this.type = CustomButtonType.primaryButton,
    this.disabled = false,
    this.horizontalPadding = 20,
    this.verticalPadding = 10,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: horizontalPadding, vertical: verticalPadding),
      child: SizedBox(
        height: MediaQuery.of(context).size.height / 17,
        width: double.infinity,
        child: ElevatedButton(
          style: ButtonStyle(
            backgroundColor: disabled
                ? MaterialStateProperty.all(getCustomButtonColor(
                    context, CustomButtonType.disabledButton))
                : MaterialStateProperty.all(
                    getCustomButtonColor(context, type)),
            foregroundColor: MaterialStateProperty.all(Colors.white),
            shape: MaterialStateProperty.all(RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(30.0),
            )),
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              Text(
                label,
                style: const TextStyle(color: Colors.white, fontSize: 16),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (icon != null)
                    Container(
                      padding: const EdgeInsets.all(7),
                      child: Icon(
                        icon,
                        color: Colors.white,
                      ),
                    ),
                ],
              ),
            ],
          ),
          onPressed: () {
            onPressed();
          },
        ),
      ),
    );
  }
}
