import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:solar_rooftop/utils/constants.dart';
import 'package:solar_rooftop/utils/profile_icon.dart';

class HomeGuardAppBar extends ConsumerWidget {
  final VoidCallback? callback;
  const HomeGuardAppBar({super.key, this.callback});
  void _onButtonPressed() {
    if (callback != null) {
      callback?.call();
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    String currentRoute =
        ModalRoute.of(context)!.settings.name ?? 'Unknown Route';
    log('Current route path: $currentRoute');
    return Container(
      color: Colors.white,
      child: GestureDetector(
        onTap: () => {},
        child: AppBar(
          automaticallyImplyLeading: false,
          flexibleSpace: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 10.0),
                  child: ProfileIcon(callback: _onButtonPressed),
                ),
                Row(
                  crossAxisAlignment:  CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      // color: Colors.red,
                      padding: EdgeInsets.only(top: 20),
                      height:200,
                      width: 100,
                      child:  SvgPicture.asset(
                                    schnellLogo,
                                    alignment: Alignment.center,
                                    color: Theme.of(context).primaryColor,
                                    fit: BoxFit.cover,
                                  ),
                    ),
                  ]
                ),
              ]),
          backgroundColor: Colors.transparent,
          elevation: 0,
        ),
      ),
    );
  }
}

class CustomAppBar extends PreferredSize {
  @override
  final Widget child;
  final double height;

  CustomAppBar({
    super.key,
    required this.height,
    required this.child,
  }) : super(child: child, preferredSize: Size.fromHeight(height));
}
