enum Environment { dev, prod }

class AppConfig {
  static Environment currentEnvironment = Environment.prod;
  static String thingsBoardUrl = 'https://app.sunrider.ai';
  static APIs get api => APIs.fromEnvironment(currentEnvironment);
}

class APIs {
  static String baseUrl = '';

  String login;

  APIs({required this.login});

  factory APIs.fromEnvironment(Environment environment) {
    switch (environment) {
      case Environment.dev:
        baseUrl =
            'https://sem0n6arji.execute-api.ap-south-1.amazonaws.com';
        return APIs(login: '$baseUrl/beta/login');
      case Environment.prod:
        baseUrl =
            'https://sem0n6arji.execute-api.ap-south-1.amazonaws.com';
        return APIs(login: '$baseUrl/api/login');
    }
  }
}
