import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:solar_rooftop/login/login_controller.dart';
import 'package:solar_rooftop/login/user_model.dart';
import 'package:solar_rooftop/utils/common_utils.dart';
import 'package:solar_rooftop/utils/constants.dart';
import 'package:solar_rooftop/utils/dialog_box.dart';

class ProfileIcon extends ConsumerWidget {
  final VoidCallback? callback;
  const ProfileIcon({super.key, this.callback});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    UserModel loggedUser = ref.watch(loginController).loggedUser;
    String? firstLetter = getFirstLetter(loggedUser.profileName ?? '');
    return SizedBox(
      height: 100,
      child: PopupMenuButton(
        onSelected: (value) {},
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(15.0))),
        icon: Stack(
          children: [
            Container(
                height: 80,
                width: 80,
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.5),
                      spreadRadius: 1,
                      blurRadius: 5,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: firstLetter != null
                    ? Center(
                        child: Text(firstLetter.toUpperCase(),
                            style: const TextStyle(
                                color: Colors.white, fontSize: 20)),
                      )
                    : const Icon(
                        Icons.person_3_rounded,
                        color: Colors.white,
                      )),
          ],
        ),

        elevation: 20,
        offset: const Offset(0, 55),
        // position: PopupMenuPosition.under,

        padding: const EdgeInsets.fromLTRB(0, 14, 8, 0),
        color: Colors.white,

        itemBuilder: (BuildContext bc) {
          return [
            PopupMenuItem(
              onTap: () {},
              child: Column(
                children: [
                  Container(
                    decoration: const BoxDecoration(
                      border: Border(
                          bottom: BorderSide(color: Colors.grey, width: 1.0)),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.account_circle_rounded,
                              color: Theme.of(context).primaryColor,
                              size: 40,
                            ),
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      loggedUser.profileName ?? '',
                                      style:  TextStyle(
                                          color: Theme.of(context).secondaryHeaderColor,
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold),
                                    ),
                                    Text(loggedUser.mobileNumber ?? '',
                                        style:  TextStyle(
                                            color: Theme.of(context).secondaryHeaderColor,
                                            fontSize: 12)),
                                  ]),
                            ),
                          ],
                        ),
                        Icon(
                          Icons.navigate_next_outlined,
                          size: 30,
                          color: Theme.of(context).primaryColor,
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
            PopupMenuItem(
                textStyle:  TextStyle(color: Theme.of(context).secondaryHeaderColor),
                value: '/logout',
                onTap: () {
                  showCustomDialog(
                    ref,
                    context,
                    "logout",
                    '',
                    '',
                  );
                },
                child:  Column(
                  children: [
                    Row(
                      children: [
                         Icon(Icons.logout, color: Theme.of(context).secondaryHeaderColor),
                        const SizedBox(
                          width: 16.0,
                          height: 40,
                        ),
                        const Text('Logout'),
                        Divider(
                          color: Theme.of(context).secondaryHeaderColor,
                          thickness: 1.0,
                        ),
                      ],
                    ),
                    const Align(
                     alignment: Alignment.centerRight,
                      child: Text(
                        'v$appVersion',
                        textAlign: TextAlign.right,
                        style: TextStyle(color: Colors.grey),
                      ),
                    )
                  ],
                )),
          ];
        },
      ),
    );
  }
}
