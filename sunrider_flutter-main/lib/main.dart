
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:solar_rooftop/firebase_options.dart';
import 'package:solar_rooftop/solar_rooftop.dart';
Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  SystemChrome.setPreferredOrientations([]).then(
    (_) {
      runApp(
        const ProviderScope(
          child: SolarRoofTop(),
        ),
      );
    },
  );
}
